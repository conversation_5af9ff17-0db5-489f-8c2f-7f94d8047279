import { isNotNull } from "drizzle-orm/expressions";
import { initDB } from "./drizzle/src/db";
import { interviews } from "./drizzle/src/schema";
import { writeFileSync } from "fs";

async function fetchDataAndSaveToJson() {
    const db = await initDB();

    try {
        console.log("Connected to the database");

        const res = await db.select({ event: interviews.event }).from(interviews).where(isNotNull(interviews.event));

        return res.map((row) => row.event);
    } catch (error) {
        console.error("Error fetching data:", error);
    }
}

fetchDataAndSaveToJson()
    .then((res) => {
        writeFileSync("events.json", JSON.stringify(res, null, 2));
        console.log("Data saved to interviews.json");
        process.exit(0);
    })
    .catch((error) => {
        console.error("Error fetching data:", error);
    });
