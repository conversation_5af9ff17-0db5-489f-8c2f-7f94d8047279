const { CloudWatchLogsClient, FilterLogEventsCommand } = require("@aws-sdk/client-cloudwatch-logs");
const fs = require('fs');

const config = {
    EVALUATION_ENDPOINT: 'https://nmrbb26y5m.execute-api.us-east-1.amazonaws.com/Prod/start-evaluation',
    API_KEY: 't2wJsZAbXr8lSAlOnLLuu4npYXbzSBeI35qTTHwA',
    BATCH_SIZE: 20,
    REPETITIONS: 1,
    MILISECONDS_BETWEEN_REPETITIONS: 0,
    MILISECONDS_BETWEEN_REQUESTS: 0,
    MAX_TIME_TO_WAIT_FOR_JOB: 10 * 60 * 1000

};

const interviewByProfile = {
    'AD': {
        candidateID: 'c32822ea-e5b7-47f4-82a0-00a6a8dceb26',
        candidate_email: '*',
        candidate_name: 'name',
        company_id: '1f90c3b0-6e76-46a4-b83b-022ca347d72c',
        embed_video: 'https://share.myinterview.com/embed/sbde3edc-d2d8-463c-810d-26d89b789d61',
        firstname: 'fistname',
        interview_URL: 'https://share.myinterview.com/video/sbde3edc-d2d8-463c-810d-26d89b789d61',
        interview_date: '2024-03-27T12:00:11.599Z',
        jobID: 'j472d9d4-1d13-429b-bb0e-cb331ba1caf5',
        jobTitle: 'AD_Pilat',
        thumbnail: 'https://video.myinterview.com/mp4/720p/feb5916c-ec44-4859-87fe-82d49855c985-00001.jpg',
        videoID: 'v6a5093d-c2e4-4b7c-9d08-f56925e0d764'
    },
    'ISC': {
        candidateID: 'c8674207-5a4f-4f0a-a552-2850354f3f1e',
        candidate_email: '*',
        candidate_name: 'name',
        company_id: '1f90c3b0-6e76-46a4-b83b-022ca347d72c',
        embed_video: 'https://share.myinterview.com/embed/s52fe26b-c867-42a3-975a-317f53d7b0f5',
        firstname: 'fistname',
        interview_URL: 'https://share.myinterview.com/video/s52fe26b-c867-42a3-975a-317f53d7b0f5',
        interview_date: '2024-03-23T12:57:41.146Z',
        jobID: 'j4e1d7b5-4992-49ff-ae14-ee66fa06f4b9',
        jobTitle: 'ISC_Pilat',
        thumbnail: 'https://video.myinterview.com/mp4/720p/f2fa7874-7e29-47be-b415-11523f007928-00001.jpg',
        videoID: 'v9e15a4d-2553-4913-a36a-c555a3a07767'
    },
    'LOG': {
        candidateID: "c05f57b8-1f72-4aa7-b061-24ad8423a045",
        candidate_email: "*",
        candidate_name: "אלי",
        company_id: "1f90c3b0-6e76-46a4-b83b-022ca347d72c",
        embed_video: "https://share.myinterview.com/embed/s5a498f1-82cb-48cf-8010-fb451142c7aa",
        firstname: "אלי",
        interview_URL: "https://share.myinterview.com/video/s5a498f1-82cb-48cf-8010-fb451142c7aa",
        interview_date: "2024-03-25T10:40:36.783Z",
        jobID: "jdf8b1ae-aab7-49bc-bfc7-414cff26943e",
        jobTitle: "LOG_Pilat",
        thumbnail: "https://video.myinterview.com/mp4/720p/f9efc160-c33f-49ed-9c60-d552aa41f054-00001.jpg",
        videoID: "v73aeeb1-0b4c-4244-84e8-a32658751f87"
    }, 'TECH': {
        candidateID: 'c2363031-c23e-4ecf-8e04-7bc52e57a46c',
        candidate_email: '*',
        candidate_name: 'עומר',
        company_id: '1f90c3b0-6e76-46a4-b83b-022ca347d72c',
        embed_video: 'https://share.myinterview.com/embed/s96437ca-4b97-4ed9-a0e1-39037223e479',
        firstname: 'עומר',
        interview_URL: 'https://share.myinterview.com/video/s96437ca-4b97-4ed9-a0e1-39037223e479',
        interview_date: '2024-03-18T09:28:17.931Z',
        jobID: 'jac094a2-75be-4c72-9034-c6def18e8be3',
        jobTitle: 'TECH_Pilat',
        thumbnail: 'https://video.myinterview.com/mp4/720p/fbbf03dc-dd69-401b-a4a7-b557463f57ba-00001.jpg',
        videoID: 'v32eca28-ac66-4b58-aa1b-acfdfa492b4f'
    }, 'FB': {
        candidateID: 'c9ef0447-e733-4e83-872b-7054c8be0b8d',
        company_id: '1f90c3b0-6e76-46a4-b83b-022ca347d72c',
        candidate_email: 'S3_permission_test',
        candidate_name: 'ויטלי',
        embed_video: 'https://share.myinterview.com/embed/se24f79c-4961-442f-b755-facb2c866b78',
        firstname: 'ויטלי',
        interview_URL: 'https://share.myinterview.com/video/se24f79c-4961-442f-b755-facb2c866b78',
        interview_date: '2023-12-19T10:54:23.945Z',
        jobID: 'j6a633c3-bacb-4ab2-b848-35fcd1bdbdef',
        jobTitle: 'FB_Pilat',
        thumbnail: 'https://video.myinterview.com/mp4/720p/f2450c0a-dbd4-4ba8-90bd-e50adcbf7027-00001.jpg',
        videoID: 'v967cf20-a454-4d8a-8917-c8130df50c64'
    },
    'SOC': {
        candidateID: 'c7343755-91db-481c-a113-48b5ddf7b542',
        candidate_email: '*',
        candidate_name: '---',
        company_id: '1f90c3b0-6e76-46a4-b83b-022ca347d72c',
        embed_video: 'https://share.myinterview.com/embed/s5870cee-46ee-40a8-98ad-2dade4d18841',
        firstname: '---',
        interview_URL: 'https://share.myinterview.com/video/s5870cee-46ee-40a8-98ad-2dade4d18841',
        interview_date: '2024-03-18T09:11:48.759Z',
        jobID: 'j1f5a9aa-3c83-4efe-bc04-f40432f6f9f1',
        jobTitle: 'SOC_Pilat',
        thumbnail: 'https://video.myinterview.com/mp4/720p/f087a386-f552-4c37-9a4e-8a92100e7596-00001.jpg',
        videoID: 'v94010cc-a01a-4986-8443-92afc42800f1'
    }
}

// Create a CloudWatch Logs client
const client = new CloudWatchLogsClient({ region: "us-east-1" });

async function getLambdaLogs(logGroupName, startTime, endTime) {
    // Set the parameters
    const params = {
        logGroupName, // for example, '/aws/lambda/your-function-name'
        startTime,    // Start time in milliseconds
        endTime, // End time in milliseconds
        // You can specify additional parameters as needed
    };

    try {
        const command = new FilterLogEventsCommand(params);
        const response = await client.send(command);
        return response.events;
    } catch (error) {
        console.error("An error occurred:", error);
    }
}

function getSpecificJob(jobName, events) {
    const jobEvents = events.filter(event => event.message.includes(jobName));
    return jobEvents;
}

function saveJobsToJSON(jobs, jobName) {
    if (jobs.length === 0) return;
    const fs = require('fs');
    fs.writeFileSync(`jobs-${jobName}.json`, JSON.stringify(jobs, null, 2));
}

async function getJobs(jobName) {
    const events = await getLambdaLogs('/aws/lambda/InterviewAnalyzerIntegrationApi', Date.now() - (240 * 60 * 60 * 1000), Date.now());
    const jobEvents = getSpecificJob(jobName, events);
    saveJobsToJSON(jobEvents, jobName);
}

function generateTestId() {
    function generateRandomHex(length) {
        let result = '';
        const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }

    function generateUUID() {
        return `test-${generateRandomHex(4)}-${generateRandomHex(4)}-${generateRandomHex(4)}-${generateRandomHex(4)}`;
    }

    return generateUUID();
}

function randomInterviewKey() {
    return Object.keys(interviewByProfile)[Math.floor(Math.random() * Object.keys(interviewByProfile).length)];
}

function getRandomInterview() {

    const interview = { ...interviewByProfile[randomInterviewKey()] };
    interview.candidate_email = generateTestId();

    return interview;
}

function generateInterviewsBatch() {
    const interviews = [];
    for (let i = 0; i < config.BATCH_SIZE; i++) {
        interviews.push(getRandomInterview());
    }

    return interviews;
}


async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function startEvaluation(interview) {
    try {
        console.log('Sending interview', interview.candidate_email)
        const response = await fetch(config.EVALUATION_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': config.API_KEY
            },
            body: JSON.stringify(interview)
        });
        return response.json();
    } catch (error) {
        console.error('Error sending interview:', error);
        fs.writeFileSync(`error.json`, JSON.stringify(error, null, 2));
    }

    return response.json();
}

async function lookForFinishedJobsInLogs(ids) {
    let finishedJobs = [];
    console.log('Looking for finished jobs', ids)
    const logs = await getLambdaLogs('/aws/lambda/InterviewAnalyzerIntegrationApi', Date.now() - (2 * 60 * 1000), Date.now());
    const relevantLogs = logs.filter(log => log.message.includes('Processing SQS message:'));
    ids.forEach(id => {
        const finished = relevantLogs.filter(log => log.message.includes(id));
        if (finished.length > 0) {
            finishedJobs.push({ id, finishTime: finished[0].timestamp });
        }
    });

    return finishedJobs;
}

function summarizeInterviews(finishedInterviews, sentInterviews) {
    const durations = finishedInterviews.map(interview => interview.duration);
    const avg = durations.reduce((acc, curr) => acc + curr, 0) / durations.length;
    const max = Math.max(...durations);
    const min = Math.min(...durations);

    const failedInterviews = sentInterviews.filter(interview => !finishedInterviews.find(finished => finished.id === interview.id));
    console.log('Average duration:', avg, 'Max duration:', max, 'Min duration:', min);
    return {
        successCount: finishedInterviews.length,
        failedCount: failedInterviews.length,
        avg,
        max,
        min,
        durations,
        finishedInterviews: finishedInterviews,
        failedInterviews: failedInterviews
    };
}


function saveInterviewsDurationsToJSON(finishedInterviews, sentInterviews) {
    console.log('saving interview durations to JSON file')
    const fileName = `batchSize-${config.BATCH_SIZE}| repetitions-${config.REPETITIONS} | testDate:${new Date()}.json`;
    const summary = summarizeInterviews(finishedInterviews, sentInterviews);
    fs.writeFileSync(fileName, JSON.stringify(summary, null, 2));
    console.log('finished saving interview durations to JSON file')
}

async function main() {
    const sentInterviews = []
    const finishedInterviews = [];
    const max_time = Date.now() + config.MAX_TIME_TO_WAIT_FOR_JOB;

    for (let i = 0; i < config.REPETITIONS; i++) {
        const interviews = generateInterviewsBatch();
        for (let j = 0; j < interviews.length; j++) {
            const interview = interviews[j];
            startEvaluation(interview);
            sentInterviews.push({ id: interview.candidate_email, sendTime: Date.now(), profile: interview.jobTitle });
            await sleep(config.MILISECONDS_BETWEEN_REQUESTS);
        }
        await sleep(config.MILISECONDS_BETWEEN_REPETITIONS);
    }

    console.log('All interviews sent, waiting for them to finish')

    while (sentInterviews.length > 0 && Date.now() < max_time) {
        const finishedJobs = await lookForFinishedJobsInLogs(sentInterviews.map(interview => interview.id));
        finishedJobs.forEach(job => {
            const finishedInterview = sentInterviews.find(interview => interview.id === job.id);
            if (finishedInterview) {
                finishedInterviews.push({ ...finishedInterview, finishTime: job.finishTime, duration: (job.finishTime - finishedInterview.sendTime) / 1000 });
                sentInterviews.splice(sentInterviews.indexOf(finishedInterview), 1);
            }
        });
        const timeLeft = Math.floor((max_time - Date.now()) / 1000);
        console.log('Finished interviews:', finishedInterviews.length, 'Remaining interviews:', sentInterviews.length, 'Time left:', timeLeft, 'seconds')
        await sleep(30 * 1000)
    }
    saveInterviewsDurationsToJSON(finishedInterviews, sentInterviews);
}

main()


