const crypto = require("crypto");
const axios = require("axios");

const getMyInterviewCredentials = (companyId) => {
    const creds = {
        "1f90c3b0-6e76-46a4-b83b-022ca347d72c": {
            accessKey: "ga5f940e-9517-46ff-b064-82e522955adc",
            secret: "ga8773d4-13b7-4986-b406-6bca0bfea018",
            companyName: "Pilat",
        },
        "c6385c4d-5f05-4b83-96b2-c7d0cf9d24f4": {
            accessKey: "ga351088-05ec-4e67-ad9d-03a00114a6f1",
            secret: "ga77350c-c4d9-457f-a166-91f6d0fb4792",
            companyName: "Iverse",
        },
        "c68d2877-c1dd-4277-a209-c1044b9386c2": {
            accessKey: "ga49a778-e592-41e5-b053-542f80df922a",
            secret: "ga123b57-ce90-4fdd-908e-921b5fc708f4",
            companyName: "Cynet",
        },
    };
    return creds[companyId];
};

const makeApiToken = (secret, clientId, timestamp) => {
    return crypto.createHmac("sha256", secret).update(`${timestamp}.${clientId}`).digest("base64");
};

const populateHeaders = (config, companyId) => {
    console.log(config)
    const timestamp = Date.now();
    const token = makeApiToken(config.secret, companyId, timestamp);
    return {
        "x-myinterview-timestamp": timestamp.toString(),
        "x-myinterview-key": config.accessKey,
        "x-myinterview-signed": token,
    };
};

const getInterviewLanguage = async (jobId, companyId) => {
    console.log("Getting interview language for job:", jobId, "company:", companyId);
    const config = getMyInterviewCredentials(companyId);
    console.log("MyInterview config:", config);
    const headers = populateHeaders(config, companyId);
    const url = `https://api-ga.myinterview.com/api/v1/jobs/${jobId}`;
    console.log(headers);
    try {
        const response = await axios.get(url, { headers });
        console.log(response.data.data);
        const language = response.data.data.language;
        return language;
    } catch (error) {
        console.error("Error fetching languages:", error);
        throw "Error fetching languages";
    }
};

const getTranscodedVideoUrls = async (videoId, companyId) => {
    const config = await getMyInterviewCredentials(companyId);
    console.log("MyInterview config:", config);
    const headers = populateHeaders(config);

    console.log(headers)
    const url = `https://api-ga.myinterview.com/api/v1/videos/${videoId}/transcoded`;

    try {
        const response = await axios.get(url, { headers });
        const videos = response.data.data.videos;
        return videos.map((video) => video.transcodedUrl);
    } catch (error) {
        console.error("Error fetching transcoded video:", error);
        throw error;
    }
};

module.exports = {
    getInterviewLanguage,
    getTranscodedVideoUrls,
};

// getInterviewLanguage("jaa07753-0c64-4d3f-9756-51813aaf23c3", "c6385c4d-5f05-4b83-96b2-c7d0cf9d24f4")
//     .then((language) => {
//         console.log("Language:", language);
//     })
//     .catch((error) => {
//         console.error("Error:", error);
//     });


getTranscodedVideoUrls("vd5a511c-d1e1-4fe4-b937-7f3e9ff55abb", "1f90c3b0-6e76-46a4-b83b-022ca347d72c").then((urls) => {
    console.log("Transcoded URLs:", urls);
}
).catch((error) => {
    // console.error("Error:", error);
});