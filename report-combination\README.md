# Report Combination

This project provides utilities for combining reports using the database module.

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Build the project:
   ```bash
   npm run build
   ```

## Development

To run the project in development mode:

```bash
npm run dev
```

## Usage

The project uses the `db` module to connect to the database and perform operations. 

Example usage:

```typescript
import { runDatabaseQuery } from 'report-combination';

async function main() {
  const result = await runDatabaseQuery();
  console.log(result);
}

main().catch(console.error);
```

## Environment Variables

The following environment variables are required:

- `ENV`: Environment (staging, production, etc.)

You can create a `.env` file in the root directory with these variables.

## License

ISC