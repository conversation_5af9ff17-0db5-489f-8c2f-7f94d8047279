{"name": "report-combination", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-secrets-manager": "^3.665.0", "@aws-sdk/client-sns": "^3.758.0", "axios": "^1.8.2", "dotenv": "^16.4.5", "drizzle-orm": "^0.33.0", "pg": "^8.13.0", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^22.7.4", "@types/pg": "^8.10.9", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}