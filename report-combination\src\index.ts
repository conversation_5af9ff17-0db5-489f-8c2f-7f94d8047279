import { eq } from 'drizzle-orm';
import { initDB, closeDBConnection, jobs, candidates, interviews, traitsFamilies, traits } from '../db';
import type { DB } from '../db';
import { InterviewReportData, ReportVersion } from '../common';

// Job ID
// '6c599c55-cfab-4620-8d3f-23a90ce9c899'

// Example function that uses the db module
export async function combineReports() {
  let db: DB | null = null;

  try {
    // Initialize the database connection
    db = await initDB();
    console.log('Connected to database');

    const res = await db
      .select({ interviewId: interviews.id })
      .from(jobs)
      .innerJoin(candidates, eq(jobs.id, candidates.jobId))
      .innerJoin(interviews, eq(candidates.id, interviews.candidateId))
      .where(eq(jobs.id, 'f026a7c8-2e60-4283-b20b-a51f25d391ec'));

    const interviewIds = res.map((interview) => interview.interviewId);

    console.log('Found', interviewIds.length, 'interviews to update');

    for (const interviewId of interviewIds) {
      const interview = await db
        .select({ report: interviews.analyzerReport })
        .from(interviews)
        .where(eq(interviews.id, interviewId));


      if (!interview[0].report) {
        continue;
      }

      console.log('Found interview', interviewId);
      
      const reportsData = interview[0].report as InterviewReportData;
      const reportVersions = reportsData.report_versions;
      
      if (reportVersions.length > 2) {
        console.log('Found report versions', reportVersions.length);
          //Trait scores that should be 0 -> A37 , A36 
          const traitScoresToZero = ['A37', 'A36'];

          const reportForTraits = reportVersions.find((report) => report.reviewed_by === 'iverse_efi_b');
          const reportForSummaries = reportVersions.find((report) => report.reviewed_by === 'iverse_boris');

          if(!reportForTraits)  {
            console.log('required report for traits not found');
            continue};
          if(!reportForSummaries){
            console.log('required report for summaries not found');
            continue;
          }  

          const performanceTraitIdsRes = await db.select({traitId:traits.id})
          .from(traitsFamilies)
          .innerJoin(traits, eq(traitsFamilies.id, traits.familyId))
          .where(eq(traitsFamilies.id, 'A47'));
          const interpersonalTraitIdsRes = await db.select({traitId:traits.id})
          .from(traitsFamilies)
          .innerJoin(traits, eq(traitsFamilies.id, traits.familyId))
          .where(eq(traitsFamilies.id, 'A48'));
          

          const performanceTraitIds = performanceTraitIdsRes.map((trait) => trait.traitId);
          const interpersonalTraitIds = interpersonalTraitIdsRes.map((trait) => trait.traitId);

          const newTraits = reportForTraits.traits.map((trait) => {
                if(traitScoresToZero.includes(trait.code)){
                    return {...trait, score:0}
                }
                return trait
            })

            const performanceTraits = newTraits.filter((trait) => performanceTraitIds.includes(trait.code)).filter(trait => trait.score > 0);
            const interpersonalTraits = newTraits.filter((trait) => interpersonalTraitIds.includes(trait.code)).filter(trait => trait.score > 0);

            const performanceFamily = reportForSummaries.families.find((family) => family.code === 'A47');
            const interpersonalFamily = reportForSummaries.families.find((family) => family.code === 'A48');
            
            
            const otherFamilies = reportForSummaries.families.filter((family) => family.code !== 'A47' && family.code !== 'A48');
            const otherFamiliesWithScoresForDisplay = reportForTraits.families.filter((family) => family.code !== 'A47' && family.code !== 'A48');

            const otherFamiliesWithScores = otherFamilies.map((family) => {
              const familyFromTraits = otherFamiliesWithScoresForDisplay.find((f) => f.code === family.code)!
              return {
                ...family,
                score: familyFromTraits.score,
              };
            });

            if(!performanceFamily || !interpersonalFamily){
              console.log('required families not found');
              continue;
            }

            if (performanceFamily && performanceTraits.length > 0) {
                performanceFamily.score = performanceTraits.reduce((acc, trait) => acc + trait.score, 0) / performanceTraits.length;
            }

            if (interpersonalFamily && interpersonalTraits.length > 0) {
                interpersonalFamily.score = interpersonalTraits.reduce((acc, trait) => acc + trait.score, 0) / interpersonalTraits.length;
            }

            const combinedFamilies = [...otherFamiliesWithScores, performanceFamily, interpersonalFamily];


        const combinedVersion:ReportVersion = {
          summary:reportForSummaries.summary,
          interview_score:combinedFamilies.reduce((acc, family) => acc + family.score, 0) / combinedFamilies.length,
          families: combinedFamilies,
          traits: [...newTraits],
          reviewed_by:'system',
          review_date: new Date().toISOString(),
        };

        const newReportsData: InterviewReportData = {
          report_versions: [...reportVersions, combinedVersion],
        };

        await db
          .update(interviews)
          .set({ analyzerReport: newReportsData })
          .where(eq(interviews.id, interviewId));
          console.log('Updated interview', interviewId);
      }

    }

    console.log('No more interviews to update');    

    return { success: true, message: 'Database query executed successfully' };
  } catch (error) {
    console.error('Error executing database query:', error);
    return { success: false, error };
  } finally {
    // Close the database connection when done
    if (db) {
      await closeDBConnection();
    }
  }
}

