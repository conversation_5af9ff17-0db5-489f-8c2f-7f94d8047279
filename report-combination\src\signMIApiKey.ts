import { createHmac } from "crypto";
import { getSecret, MyInterviewSecrets } from "../common";

function signMIApiKey(objectId: string, secretKey: string, securityLevel = "apikey") {
    const key = `${securityLevel} ${objectId} sig=`;
    const hmac = createHmac('sha256', secretKey);
    hmac.update(key.replace(/\s*/gi, ''));
    const signature = hmac.digest('hex');
    return `${key}${signature}`;
};



export async function createWidgetApiKey(){
    const accountSecrets = await getSecret<MyInterviewSecrets>('myInterviewCreds/c6385c4d-5f05-4b83-96b2-c7d0cf9d24f4')
    const key = signMIApiKey(accountSecrets.apiKey, accountSecrets.apiKey,'apikey');
    return key;
}