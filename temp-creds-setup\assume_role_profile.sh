#!/bin/bash

# Variables
ROLE_ARN="arn:aws:iam::405424696292:role/cli_role"
SESSION_NAME="my-amplify-session" # Use a descriptive name
MFA_DEVICE_NAME="minimal_user"
MFA_ARN="arn:aws:iam::405424696292:mfa/$MFA_DEVICE_NAME"
TEMP_PROFILE_NAME="temp-assumed-role" # Name for the temporary profile
AWS_REGION="us-east-1" # CHANGE THIS to your desired AWS region

# Unset any existing AWS-related environment variables that might interfere
unset AWS_ACCESS_KEY_ID
unset AWS_SECRET_ACCESS_KEY
unset AWS_SESSION_TOKEN
unset AWS_PROFILE

# Prompt for MFA code
read -sp "Enter MFA code for $MFA_ARN: " MFA_CODE
echo "" # Newline after password prompt

# Assume the role with MFA
echo "Attempting to assume role $ROLE_ARN..."
TEMP_CREDENTIALS=$(aws sts assume-role \
  --role-arn "$ROLE_ARN" \
  --role-session-name "$SESSION_NAME" \
  --serial-number "$MFA_ARN" \
  --token-code "$MFA_CODE" \
  --duration-seconds 3600 \
  --output json) # Ensure output is JSON

# Check if assume-role was successful
if [ $? -ne 0 ] || ! echo "$TEMP_CREDENTIALS" | jq -e '.Credentials.AccessKeyId' > /dev/null; then
    echo "Failed to assume role. Check details and MFA code."
    echo "AWS STS response:"
    echo "$TEMP_CREDENTIALS"
    exit 1
fi

# Extract credentials using jq
ACCESS_KEY_ID=$(echo "$TEMP_CREDENTIALS" | jq -r '.Credentials.AccessKeyId')
SECRET_ACCESS_KEY=$(echo "$TEMP_CREDENTIALS" | jq -r '.Credentials.SecretAccessKey')
SESSION_TOKEN=$(echo "$TEMP_CREDENTIALS" | jq -r '.Credentials.SessionToken')

# Configure the temporary profile
echo "Configuring temporary profile '$TEMP_PROFILE_NAME'..."
aws configure set profile."$TEMP_PROFILE_NAME".aws_access_key_id "$ACCESS_KEY_ID"
aws configure set profile."$TEMP_PROFILE_NAME".aws_secret_access_key "$SECRET_ACCESS_KEY"
aws configure set profile."$TEMP_PROFILE_NAME".aws_session_token "$SESSION_TOKEN"
aws configure set profile."$TEMP_PROFILE_NAME".region "$AWS_REGION" # Amplify often needs region

echo ""
echo "Temporary profile '$TEMP_PROFILE_NAME' configured successfully."
echo "Use '--profile $TEMP_PROFILE_NAME' with AWS CLI or Amplify commands."
echo "Example: aws s3 ls --profile $TEMP_PROFILE_NAME"
echo "Example: amplify status --profile $TEMP_PROFILE_NAME"
echo "Credentials expire at: $(echo "$TEMP_CREDENTIALS" | jq -r '.Credentials.Expiration')"
echo ""
echo "**Important:** Remember to remove or refresh this profile when credentials expire.**"

# Optional: Set the AWS_PROFILE env var for this session
# export AWS_PROFILE=$TEMP_PROFILE_NAME
# echo "(Optional) Exported AWS_PROFILE=$TEMP_PROFILE_NAME for this session."