#!/bin/bash

# Variables
ROLE_ARN="arn:aws:iam::405424696292:role/cli_role"  # Replace with your role ARN
SESSION_NAME="my_session"
MFA_DEVICE_NAME="minimal_user"
MFA_ARN="arn:aws:iam::405424696292:mfa/$MFA_DEVICE_NAME"

# Unset any existing AWS-related environment variables
unset AWS_ACCESS_KEY_ID
unset AWS_SECRET_ACCESS_KEY
unset AWS_SESSION_TOKEN

# Prompt for MFA code
read -p "Enter MFA code: " MFA_CODE

# Assume the role with MFA
TEMP_CREDENTIALS=$(aws sts assume-role --role-arn $ROLE_ARN --role-session-name $SESSION_NAME --serial-number $MFA_ARN --token-code $MFA_CODE --duration-seconds 3600)

if echo $TEMP_CREDENTIALS | grep -q "AccessKeyId"; then
    export AWS_ACCESS_KEY_ID=$(echo $TEMP_CREDENTIALS | jq -r '.Credentials.AccessKeyId')
    export AWS_SECRET_ACCESS_KEY=$(echo $TEMP_CREDENTIALS | jq -r '.Credentials.SecretAccessKey')
    export AWS_SESSION_TOKEN=$(echo $TEMP_CREDENTIALS | jq -r '.Credentials.SessionToken')
    echo "Temporary credentials set. You can now use the AWS CLI with these credentials."
else
    echo "Failed to assume role. Check the role ARN, MFA ARN, and try again."
    echo $TEMP_CREDENTIALS
fi
